from unittest.mock import MagicMock
from uuid import uuid4

from fastapi import <PERSON>TT<PERSON><PERSON>x<PERSON>, status

from api.orders.endpoints import create_order
from api.orders.schemas import CreateOrderRequest, CreateOrderResponse
from common.parser import ParsingError
from orders.domain import model
from orders.services import AbstractOrdersService

import pytest  # isort: skip


class TestCreateOrder:
    def test_create_order_success(self, api_security):
        mock_orders_service = MagicMock(spec=AbstractOrdersService)
        mock_orders_service.create_order.return_value = model.OrderResponse(
            order_uuid=uuid4(), message="Order created successfully"
        )

        mock_auth, mock_request = api_security("/v1/glass/order")
        mock_request.method = "POST"

        order_request = CreateOrderRequest(
            customer_details=model.OrderCustomer(
                customer_id=1,
                customer_reference="Ref123",
                customer_name="Test Customer",
                customer_account_name="Test Account",
                customer_account_id="077ba04b-82f6-41a6-9d54-5a793a28fee6",
                person_placing_order="Test User",
                customer_email="<EMAIL>",
                customer_contact_no="**********",
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="John Doe",
                address_line1="123 Test Street",
                address_line2="",
                city="Test City",
                state_or_region="Test State",
                postal_code="12345",
                country="Test Country",
            ),
            order_items=[
                model.OrderItem(sim_type="2FF (Mini) SIMs", quantity=100),
                model.OrderItem(sim_type="3FF (Micro) SIMs", quantity=50),
            ],
            order_by="test_user",
        )

        response = create_order(
            request=mock_request,
            order=order_request,
            orders_service=mock_orders_service,
            trace_id=uuid4(),
        )

        assert isinstance(response, CreateOrderResponse)
        assert response.message == "Order created successfully"
        assert response.uuid is not None
        mock_orders_service.create_order.assert_called_once()

    def test_create_order_with_minimal_data(self, api_security):
        mock_orders_service = MagicMock(spec=AbstractOrdersService)
        mock_orders_service.create_order.return_value = model.OrderResponse(
            order_uuid=uuid4(), message="Order created successfully"
        )

        mock_auth, mock_request = api_security("/v1/glass/order")
        mock_request.method = "POST"

        order_request = CreateOrderRequest(
            customer_details=model.OrderCustomer(
                customer_id=1,
                customer_reference="Ref123",
                customer_name="Minimal Customer",
                customer_account_name="Minimal Account",
                person_placing_order="Test User",
                customer_account_id="077ba04b-82f6-41a6-9d54-5a793a28fee6",
                customer_email="<EMAIL>",
                customer_contact_no="**********",
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="Jane Doe",
                address_line1="456 Minimal Street",
                address_line2="",
                city="Test City",
                state_or_region="Test State",
                postal_code="12345",
                country="Test Country",
            ),
            order_items=[model.OrderItem(sim_type="4FF (Nano) SIMs", quantity=25)],
            order_by="test_user",
        )

        response = create_order(
            request=mock_request,
            order=order_request,
            orders_service=mock_orders_service,
            trace_id=uuid4(),
        )

        assert isinstance(response, CreateOrderResponse)
        assert response.message == "Order created successfully"
        mock_orders_service.create_order.assert_called_once()

    def test_create_order_parsing_error(self, api_security):
        mock_orders_service = MagicMock(spec=AbstractOrdersService)
        mock_orders_service.create_order.side_effect = ParsingError(
            "Invalid order data"
        )

        mock_auth, mock_request = api_security("/v1/glass/order")
        mock_request.method = "POST"

        order_request = CreateOrderRequest(
            customer_details=model.OrderCustomer(
                customer_id=1,
                customer_reference="Ref123",
                customer_name="Error Customer",
                person_placing_order="Test User",
                customer_account_name="Error Account",
                customer_account_id="077ba04b-82f6-41a6-9d54-5a793a28fee6",
                customer_email="<EMAIL>",
                customer_contact_no="**********",
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="Error Contact",
                address_line1="Error Street",
                address_line2="",
                city="Test City",
                state_or_region="Test State",
                postal_code="12345",
                country="Test Country",
            ),
            order_items=[model.OrderItem(sim_type="Invalid SIM", quantity=0)],
            order_by="test_user",
        )

        with pytest.raises(HTTPException) as exc_info:
            create_order(
                request=mock_request,
                order=order_request,
                orders_service=mock_orders_service,
                trace_id=uuid4(),
            )

        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert exc_info.value.detail == "Invalid order data"

    def test_create_order_generic_exception(self, api_security):
        mock_orders_service = MagicMock(spec=AbstractOrdersService)
        mock_orders_service.create_order.side_effect = Exception(
            "Database connection error"
        )

        mock_auth, mock_request = api_security("/v1/glass/order")
        mock_request.method = "POST"

        order_request = CreateOrderRequest(
            customer_details=model.OrderCustomer(
                customer_name="Exception Customer",
                customer_id=1,
                customer_reference="Ref123",
                customer_account_name="Exception Account",
                person_placing_order="Test User",
                customer_account_id="077ba04b-82f6-41a6-9d54-5a793a28fee6",
                customer_email="<EMAIL>",
                customer_contact_no="**********",
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="Exception Contact",
                address_line1="Exception Street",
                address_line2="",
                city="Test City",
                state_or_region="Test State",
                postal_code="12345",
                country="Test Country",
            ),
            order_items=[model.OrderItem(sim_type="2FF (Mini) SIMs", quantity=100)],
            order_by="test_user",
        )

        with pytest.raises(HTTPException) as exc_info:
            create_order(
                request=mock_request,
                order=order_request,
                orders_service=mock_orders_service,
                trace_id=uuid4(),
            )

        assert exc_info.value.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        assert "Couldn't process the order request" in exc_info.value.detail

    def test_create_order_empty_order_items(self, api_security):
        mock_orders_service = MagicMock(spec=AbstractOrdersService)
        mock_orders_service.create_order.return_value = model.OrderResponse(
            order_uuid=uuid4(), message="Order created successfully"
        )

        mock_auth, mock_request = api_security("/v1/glass/order")
        mock_request.method = "POST"

        order_request = CreateOrderRequest(
            customer_details=model.OrderCustomer(
                customer_name="Empty Items Customer",
                customer_id=1,
                customer_reference="Ref123",
                customer_account_name="Empty Items Account",
                person_placing_order="Test User",
                customer_account_id="077ba04b-82f6-41a6-9d54-5a793a28fee6",
                customer_email="<EMAIL>",
                customer_contact_no="**********",
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="Empty Items Contact",
                address_line1="Empty Items Street",
                address_line2="",
                city="Test City",
                state_or_region="Test State",
                postal_code="12345",
                country="Test Country",
            ),
            order_items=[],
            order_by="test_user",
        )

        response = create_order(
            request=mock_request,
            order=order_request,
            orders_service=mock_orders_service,
            trace_id=uuid4(),
        )

        assert isinstance(response, CreateOrderResponse)
        mock_orders_service.create_order.assert_called_once()

    def test_create_order_multiple_order_items(self, api_security):
        mock_orders_service = MagicMock(spec=AbstractOrdersService)
        mock_orders_service.create_order.return_value = model.OrderResponse(
            order_uuid=uuid4(), message="Order created successfully"
        )

        mock_auth, mock_request = api_security("/v1/glass/order")
        mock_request.method = "POST"

        order_request = CreateOrderRequest(
            customer_details=model.OrderCustomer(
                customer_name="Multi Items Customer",
                customer_id=1,
                customer_reference="Ref123",
                person_placing_order="Test User",
                customer_account_name="Multi Items Account",
                customer_account_id="077ba04b-82f6-41a6-9d54-5a793a28fee6",
                customer_email="<EMAIL>",
                customer_contact_no="**********",
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="Multi Items Contact",
                address_line1="Multi Items Street",
                address_line2="",
                city="Test City",
                state_or_region="Test State",
                postal_code="12345",
                country="Test Country",
            ),
            order_items=[
                model.OrderItem(sim_type="2FF (Mini) SIMs", quantity=500),
                model.OrderItem(sim_type="3FF (Micro) SIMs", quantity=300),
                model.OrderItem(sim_type="4FF (Nano) SIMs", quantity=200),
            ],
            order_by="test_user",
        )

        response = create_order(
            request=mock_request,
            order=order_request,
            orders_service=mock_orders_service,
            trace_id=uuid4(),
        )

        assert isinstance(response, CreateOrderResponse)
        mock_orders_service.create_order.assert_called_once()

        # Verify the order model conversion
        call_args = mock_orders_service.create_order.call_args[1]["order"]
        assert len(call_args.order_items) == 3
        assert call_args.customer_details.customer_name == "Multi Items Customer"
