from dataclasses import dataclass
from datetime import datetime
from typing import List
from uuid import UUID

from pydantic import EmailStr, Field


@dataclass
class Order:
    order_by: str
    uuid: UUID | None = None
    id: int | None = None
    order_date: datetime | None = None


@dataclass
class OrderCustomer:
    customer_id: str
    customer_email: EmailStr
    customer_contact_no: str
    customer_name: str = Field(min_length=1, max_length=60)
    customer_account_name: str = Field(min_length=1, max_length=60)
    person_placing_order: str | None = None
    customer_reference: str | None = None
    uuid: UUID | None = None
    id: int | None = None
    customer_account_id: UUID | None = None
    customer_account_logo_url: str | None = None
    order_id: UUID | None = None


@dataclass
class OrderShippingDetails:
    contact_name: str
    address_line1: str
    address_line2: str
    city: str
    state_or_region: str
    postal_code: str
    country: str
    uuid: UUID | None = None
    id: int | None = None
    other_information: str | None = None
    order_id: UUID | None = None


@dataclass
class OrderItem:
    uuid: UUID | None = None
    id: int | None = None
    order_id: UUID | None = None
    sim_type: str | None = None
    quantity: int | None = None


@dataclass
class OrderStatusHistory:
    uuid: UUID | None = None
    id: int | None = None
    status_name: str | None = None
    status_date: datetime | None = None
    order_id: UUID | None = None


@dataclass
class OrderTracking:
    uuid: UUID | None = None
    id: int | None = None
    reference_id: str | None = None
    reference_url: str | None = None
    order_id: UUID | None = None


@dataclass
class OrderRequest:
    order_by: str
    customer_details: OrderCustomer
    order_items: List[OrderItem]
    shipping_details: OrderShippingDetails
    order_status_history: OrderStatusHistory


@dataclass
class OrderResponse:
    message: str
    order_uuid: UUID | None = None


@dataclass
class OrdersData:
    order_id: int
    order_by: str
    customer_account_name: str
    order_date: datetime
    customer_name: str
    person_placing_order: str
    customer_email: str
    customer_phone: str
    order_status_history: str
    order_item: List[OrderItem]
    customer_account_logo_url: str | None = None
