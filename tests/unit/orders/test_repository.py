from datetime import datetime
from unittest.mock import MagicMock, create_autospec
from uuid import uuid4

import pytest
from sqlalchemy.orm import Session

from api.orders.schemas import TrackingInfo, UpdateOrderStatusRequest, UpdateOrderStatusResponse
from common.parser import ParsingError
from orders.adapters.repository import DatabaseOrdersRepository
from orders.domain import model


class TestDatabaseOrdersRepository:
    @pytest.fixture
    def session_mock(self):
        return MagicMock(spec=Session)

    @pytest.fixture
    def repository(self, session_mock):
        return DatabaseOrdersRepository(session=session_mock)

    def test_update_order_status_success(self, repository, session_mock):
        # Arrange
        order_id = uuid4()
        update_data = UpdateOrderStatusRequest(status="PROCESSING")  # Use non-shipped status

        # Mock order exists
        mock_order = MagicMock()
        session_mock.query.return_value.filter.return_value.first.return_value = mock_order

        # Mock status doesn't already exist
        session_mock.query.return_value.scalar.return_value = False

        # Act
        result = repository.update_order_status(order_id=order_id, update_data=update_data)

        # Assert
        assert isinstance(result, UpdateOrderStatusResponse)
        assert result.order_id == order_id
        assert result.status == "PROCESSING"
        assert result.message == "Order status updated successfully"
        session_mock.add.assert_called()
        session_mock.commit.assert_called_once()

    def test_update_order_status_with_tracking(self, repository, session_mock):
        # Arrange
        order_id = uuid4()
        tracking_info = TrackingInfo(
            reference_id="TRACK123",
            reference_url="https://tracking.example.com/TRACK123"
        )
        update_data = UpdateOrderStatusRequest(status="SHIPPED", tracking=tracking_info)

        # Mock order exists
        mock_order = MagicMock()
        session_mock.query.return_value.filter.return_value.first.return_value = mock_order

        # Mock status doesn't already exist
        session_mock.query.return_value.scalar.return_value = False

        # Act
        result = repository.update_order_status(order_id=order_id, update_data=update_data)

        # Assert
        assert isinstance(result, UpdateOrderStatusResponse)
        assert result.order_id == order_id
        assert result.status == "SHIPPED"
        assert result.message == "Order status updated successfully"
        # Should be called twice: once for tracking, once for status history
        assert session_mock.add.call_count >= 2
        session_mock.commit.assert_called_once()

    def test_update_order_status_order_not_found(self, repository, session_mock):
        # Arrange
        order_id = uuid4()
        update_data = UpdateOrderStatusRequest(status="SHIPPED")

        # Mock order doesn't exist
        session_mock.query.return_value.filter.return_value.first.return_value = None

        # Act & Assert
        with pytest.raises(ParsingError) as exc_info:
            repository.update_order_status(order_id=order_id, update_data=update_data)

        assert f"Order with ID {order_id} not found" in str(exc_info.value)

    def test_update_order_status_already_same_status(self, repository, session_mock):
        # Arrange
        order_id = uuid4()
        update_data = UpdateOrderStatusRequest(status="SHIPPED")

        # Mock order exists
        mock_order = MagicMock()
        session_mock.query.return_value.filter.return_value.first.return_value = mock_order

        # Mock status already exists
        session_mock.query.return_value.scalar.return_value = True

        # Act & Assert
        with pytest.raises(ParsingError) as exc_info:
            repository.update_order_status(order_id=order_id, update_data=update_data)

        assert "Cannot update status, Already in the same status" in str(exc_info.value)

    def test_update_order_status_shipped_without_tracking(self, repository, session_mock):
        # Arrange
        order_id = uuid4()
        update_data = UpdateOrderStatusRequest(status="SHIPPED")  # No tracking info

        # Mock order exists
        mock_order = MagicMock()
        session_mock.query.return_value.filter.return_value.first.return_value = mock_order

        # Mock status doesn't already exist
        session_mock.query.return_value.scalar.return_value = False

        # Act & Assert
        with pytest.raises(ParsingError) as exc_info:
            repository.update_order_status(order_id=order_id, update_data=update_data)

        assert "Tracking information is required for shipped orders" in str(exc_info.value)

    def test_update_order_status_shipped_missing_reference_id(self, repository, session_mock):
        # Arrange
        order_id = uuid4()
        tracking_info = TrackingInfo(
            reference_id=None,  # Missing reference ID
            reference_url="https://tracking.example.com/TRACK123"
        )
        update_data = UpdateOrderStatusRequest(status="SHIPPED", tracking=tracking_info)

        # Mock order exists
        mock_order = MagicMock()
        session_mock.query.return_value.filter.return_value.first.return_value = mock_order

        # Mock status doesn't already exist
        session_mock.query.return_value.scalar.return_value = False

        # Act & Assert
        with pytest.raises(ParsingError) as exc_info:
            repository.update_order_status(order_id=order_id, update_data=update_data)

        assert "Tracking reference ID is required for shipped orders" in str(exc_info.value)

    def test_update_order_status_shipped_missing_reference_url(self, repository, session_mock):
        # Arrange
        order_id = uuid4()
        tracking_info = TrackingInfo(
            reference_id="TRACK123",
            reference_url=None  # Missing reference URL
        )
        update_data = UpdateOrderStatusRequest(status="SHIPPED", tracking=tracking_info)

        # Mock order exists
        mock_order = MagicMock()
        session_mock.query.return_value.filter.return_value.first.return_value = mock_order

        # Mock status doesn't already exist
        session_mock.query.return_value.scalar.return_value = False

        # Act & Assert
        with pytest.raises(ParsingError) as exc_info:
            repository.update_order_status(order_id=order_id, update_data=update_data)

        assert "Tracking reference URL is required for shipped orders" in str(exc_info.value)

    def test_create_order_success(self, repository, session_mock):
        # Arrange
        order_request = model.OrderRequest(
            order_by="test_user",
            customer_details=model.OrderCustomer(
                customer_id="1",
                customer_email="<EMAIL>",
                customer_contact_no="**********",
                customer_name="John Doe",
                customer_account_name="Test Account"
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="John Doe",
                address_line1="123 Test St",
                address_line2="",
                city="Test City",
                state_or_region="Test State",
                postal_code="12345",
                country="Test Country"
            ),
            order_items=[
                model.OrderItem(sim_type="2FF (Mini) SIMs", quantity=100)
            ],
            order_status_history=model.OrderStatusHistory()
        )

        # Mock successful database operations
        mock_order = MagicMock()
        mock_order.uuid = uuid4()
        session_mock.add.return_value = None
        session_mock.flush.return_value = None
        session_mock.commit.return_value = None

        # Act
        result = repository.create_order(order=order_request)

        # Assert
        assert isinstance(result, model.OrderResponse)
        assert result.message == "Order created successfully"
        session_mock.add.assert_called()
        session_mock.flush.assert_called_once()
        session_mock.commit.assert_called_once()

    def test_get_order_details_success(self, repository, session_mock):
        # Arrange
        order_id = uuid4()
        mock_order = MagicMock()
        mock_order.id = 1
        mock_order.uuid = order_id
        mock_order.order_by = "test_user"
        mock_order.order_date = datetime(2024, 1, 1, 12, 0, 0)

        # Mock relationships
        mock_customer = model.OrderCustomer(
            customer_id=uuid4(),
            customer_email="<EMAIL>",
            customer_contact_no="**********",
            customer_name="John Doe",
            customer_account_name="Test Account"
        )
        mock_shipping = model.OrderShippingDetails(
            contact_name="John Doe",
            address_line1="123 Test St",
            address_line2="",
            city="Test City",
            state_or_region="Test State",
            postal_code="12345",
            country="Test Country"
        )
        mock_item = model.OrderItem(sim_type="2FF (Mini) SIMs", quantity=100)
        mock_status = model.OrderStatusHistory(
            status_name="PENDING",
            status_date=datetime(2024, 1, 1, 12, 0, 0)
        )

        mock_order.customers = [mock_customer]
        mock_order.shipping_details = [mock_shipping]
        mock_order.items = [mock_item]
        mock_order.status_history = [mock_status]
        mock_order.tracking = []

        session_mock.query.return_value.options.return_value.filter.return_value.first.return_value = mock_order

        # Act
        result = repository.get_order_details(order_id=order_id)

        # Assert
        assert isinstance(result, model.OrderDetailsResponse)
        assert result.order_id == order_id
        assert result.customer_details == mock_customer
        assert result.shipping_details == mock_shipping
        assert len(result.order_items) == 1
        assert result.order_status_history == mock_status
        assert result.order_tracking is None

    def test_get_order_details_not_found(self, repository, session_mock):
        # Arrange
        order_id = uuid4()
        session_mock.query.return_value.options.return_value.filter.return_value.first.return_value = None

        # Act & Assert
        with pytest.raises(ParsingError) as exc_info:
            repository.get_order_details(order_id=order_id)

        assert f"Order with ID {order_id} not found" in str(exc_info.value)

    def test_get_orders_success(self, repository, session_mock):
        # Arrange
        mock_results = [
            {
                "order_id": uuid4(),
                "order_by": "test_user",
                "customer_account_name": "Test Account",
                "person_placing_order": "Test User",
                "customer_account_logo_url": "https://example.com/logo.png",
                "order_date": datetime(2024, 1, 1, 12, 0, 0),
                "customer_name": "John Doe",
                "customer_email": "<EMAIL>",
                "customer_phone": "**********",
                "order_status_history": "PENDING",
                "sim_details": [{"type": "2FF (Mini) SIMs", "quantity": 100}]
            }
        ]

        session_mock.execute.return_value.mappings.return_value = mock_results

        # Act
        result = repository.get_orders(
            account_id=None,
            customer_id=None,
            customer_account_name=None,
            order_date=None,
            customer_name=None,
            customer_email=None,
            customer_contact_no=None,
            status_type=None,
            pagination=None,
            ordering=None,
            searching=None,
        )

        # Assert
        assert len(result) == 1
        assert isinstance(result[0], model.OrdersData)
        assert result[0].customer_name == "John Doe"
        session_mock.execute.assert_called_once()

    def test_get_orders_with_filters(self, repository, session_mock):
        # Arrange
        from common.pagination import Pagination
        from common.ordering import Ordering, OrderDirection
        from common.searching import Searching

        pagination = Pagination(page=1, page_size=10)
        # Fix Ordering validation - use OrderDirection enum only
        ordering = Ordering(field="order_date", order=OrderDirection.DESC)
        searching = Searching(search="test")

        mock_results = []
        session_mock.execute.return_value.mappings.return_value = mock_results

        # Act
        result = repository.get_orders(
            account_id=1,
            customer_id=uuid4(),
            customer_account_name="Test Account",
            order_date=None,
            customer_name="John",
            customer_email="<EMAIL>",
            customer_contact_no="123",
            status_type="PENDING",
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )

        # Assert
        assert len(result) == 0
        session_mock.execute.assert_called_once()

    def test_get_orders_count_success(self, repository, session_mock):
        # Arrange
        mock_orders = [
            model.OrdersData(
                order_id=uuid4(),
                order_by="test_user",
                customer_account_name="Test Account",
                order_date=datetime(2024, 1, 1, 12, 0, 0),
                customer_name="John Doe",
                person_placing_order="Test User",
                customer_email="<EMAIL>",
                customer_phone="**********",
                order_status_history="PENDING",
                order_item=[],
                customer_account_logo_url="https://example.com/logo.png"
            )
        ]

        # Mock the get_orders method to return mock data
        repository.get_orders = MagicMock(return_value=mock_orders)

        # Act
        result = repository.get_orders_count(
            account_id=None,
            customer_id=None,
            customer_account_name=None,
            order_date=None,
            customer_name=None,
            customer_email=None,
            customer_contact_no=None,
            status_type=None,
            searching=None,
        )

        # Assert
        assert result == 1
        repository.get_orders.assert_called_once()
