from datetime import datetime
from unittest.mock import MagicMock
from uuid import uuid4

import pytest

from api.orders.schemas import TrackingInfo, UpdateOrderStatusRequest, UpdateOrderStatusResponse
from common.ordering import Ordering, OrderDirection
from common.pagination import Pagination
from common.searching import Searching
from orders.adapters.repository import AbstractOrdersRepository
from orders.domain import model
from orders.exceptions import NoOrdersLogs
from orders.services import OrdersService


class TestOrdersService:
    @pytest.fixture
    def mock_repository(self):
        return MagicMock(spec=AbstractOrdersRepository)

    @pytest.fixture
    def orders_service(self, mock_repository):
        return OrdersService(orders_repository=mock_repository)

    def test_update_order_status_success(self, orders_service, mock_repository):
        # Arrange
        order_id = uuid4()
        update_data = UpdateOrderStatusRequest(status="SHIPPED")
        expected_response = UpdateOrderStatusResponse(
            order_id=order_id,
            status="SHIPPED",
            message="Order status updated successfully"
        )
        mock_repository.update_order_status.return_value = expected_response

        # Act
        result = orders_service.update_order_status(
            order_id=order_id, update_data=update_data
        )

        # Assert
        assert result == expected_response
        mock_repository.update_order_status.assert_called_once_with(
            order_id=order_id, update_data=update_data
        )

    def test_update_order_status_with_tracking(self, orders_service, mock_repository):
        # Arrange
        order_id = uuid4()
        from api.orders.schemas import TrackingInfo
        tracking_info = TrackingInfo(
            reference_id="TRACK123",
            reference_url="https://tracking.example.com/TRACK123"
        )
        update_data = UpdateOrderStatusRequest(status="SHIPPED", tracking=tracking_info)
        expected_response = UpdateOrderStatusResponse(
            order_id=order_id,
            status="SHIPPED",
            message="Order status updated successfully"
        )
        mock_repository.update_order_status.return_value = expected_response

        # Act
        result = orders_service.update_order_status(
            order_id=order_id, update_data=update_data
        )

        # Assert
        assert result == expected_response
        mock_repository.update_order_status.assert_called_once_with(
            order_id=order_id, update_data=update_data
        )

    def test_update_order_status_repository_error(self, orders_service, mock_repository):
        # Arrange
        order_id = uuid4()
        update_data = UpdateOrderStatusRequest(status="SHIPPED")
        mock_repository.update_order_status.side_effect = Exception("Database error")

        # Act & Assert
        with pytest.raises(Exception) as exc_info:
            orders_service.update_order_status(order_id=order_id, update_data=update_data)

        assert str(exc_info.value) == "Database error"
        mock_repository.update_order_status.assert_called_once_with(
            order_id=order_id, update_data=update_data
        )

    def test_create_order_success(self, orders_service, mock_repository):
        # Arrange
        order_request = model.OrderRequest(
            order_by="test_user",
            customer_details=model.OrderCustomer(
                customer_id="1",
                customer_email="<EMAIL>",
                customer_contact_no="**********",
                customer_name="John Doe",
                customer_account_name="Test Account"
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="John Doe",
                address_line1="123 Test St",
                address_line2="",
                city="Test City",
                state_or_region="Test State",
                postal_code="12345",
                country="Test Country"
            ),
            order_items=[
                model.OrderItem(sim_type="2FF (Mini) SIMs", quantity=100)
            ],
            order_status_history=model.OrderStatusHistory()
        )
        expected_response = model.OrderResponse(
            order_uuid=uuid4(),
            message="Order created successfully"
        )
        mock_repository.create_order.return_value = expected_response

        # Act
        result = orders_service.create_order(order=order_request)

        # Assert
        assert result == expected_response
        mock_repository.create_order.assert_called_once_with(order=order_request)

    def test_get_orders_success(self, orders_service, mock_repository):
        # Arrange
        expected_orders = [
            model.OrdersData(
                order_id=1,
                order_by="test_user",
                customer_account_name="Test Account",
                order_date="2024-01-01T12:00:00",
                customer_name="John Doe",
                person_placing_order="Test User",
                customer_email="<EMAIL>",
                customer_phone="**********",
                order_status_history="PENDING",
                order_item=[],
                customer_account_logo_url="https://example.com/logo.png"
            )
        ]
        expected_count = 1
        mock_repository.get_orders.return_value = expected_orders
        mock_repository.get_orders_count.return_value = expected_count

        # Act
        result_orders, result_count = orders_service.get_orders(
            account_id=None,
            customer_id=None,
            customer_account_name=None,
            order_date=None,
            customer_name=None,
            customer_email=None,
            customer_contact_no=None,
            status_type=None,
            pagination=None,
            ordering=None,
            searching=None,
        )

        # Assert
        assert result_orders == expected_orders
        assert result_count == expected_count
        mock_repository.get_orders.assert_called_once()
        mock_repository.get_orders_count.assert_called_once()

    def test_get_order_details_success(self, orders_service, mock_repository):
        # Arrange
        order_id = uuid4()
        expected_order_details = model.OrderDetailsResponse(
            id=1,
            order_id=order_id,
            order_by="test_user",
            order_date=datetime(2024, 1, 1, 12, 0, 0),
            customer_details=model.OrderCustomer(
                customer_id=uuid4(),
                customer_email="<EMAIL>",
                customer_contact_no="**********",
                customer_name="John Doe",
                customer_account_name="Test Account"
            ),
            shipping_details=model.OrderShippingDetails(
                contact_name="John Doe",
                address_line1="123 Test St",
                address_line2="",
                city="Test City",
                state_or_region="Test State",
                postal_code="12345",
                country="Test Country"
            ),
            order_items=[
                model.OrderItem(sim_type="2FF (Mini) SIMs", quantity=100)
            ],
            order_status_history=model.OrderStatusHistory(
                status_name="PENDING",
                status_date=datetime(2024, 1, 1, 12, 0, 0)
            ),
            order_tracking=None
        )
        mock_repository.get_order_details.return_value = expected_order_details

        # Act
        result = orders_service.get_order_details(order_id=order_id)

        # Assert
        assert result == expected_order_details
        mock_repository.get_order_details.assert_called_once_with(order_id=order_id)

    def test_get_order_details_repository_error(self, orders_service, mock_repository):
        # Arrange
        order_id = uuid4()
        mock_repository.get_order_details.side_effect = Exception("Database error")

        # Act & Assert
        with pytest.raises(Exception) as exc_info:
            orders_service.get_order_details(order_id=order_id)

        assert str(exc_info.value) == "Database error"
        mock_repository.get_order_details.assert_called_once_with(order_id=order_id)

    def test_get_orders_with_filters_success(self, orders_service, mock_repository):
        # Arrange
        expected_orders = [
            model.OrdersData(
                order_id=uuid4(),
                order_by="test_user",
                customer_account_name="Test Account",
                order_date=datetime(2024, 1, 1, 12, 0, 0),
                customer_name="John Doe",
                person_placing_order="Test User",
                customer_email="<EMAIL>",
                customer_phone="**********",
                order_status_history="PENDING",
                order_item=[],
                customer_account_logo_url="https://example.com/logo.png"
            )
        ]
        expected_count = 1
        mock_repository.get_orders.return_value = expected_orders
        mock_repository.get_orders_count.return_value = expected_count

        pagination = Pagination(page=1, page_size=10)
        # Fix Ordering validation - use OrderDirection enum
        ordering = Ordering(field="order_date", order=OrderDirection.DESC)
        searching = Searching(search="test")

        # Act
        result_orders, result_count = orders_service.get_orders(
            account_id=1,
            customer_id=uuid4(),
            customer_account_name="Test Account",
            order_date=datetime(2024, 1, 1),
            customer_name="John",
            customer_email="<EMAIL>",
            customer_contact_no="123",
            status_type="PENDING",
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )

        # Assert
        assert result_orders == expected_orders
        assert result_count == expected_count
        mock_repository.get_orders.assert_called_once()
        mock_repository.get_orders_count.assert_called_once()

    def test_get_orders_no_orders_logs_exception(self, orders_service, mock_repository):
        # Arrange
        # Fix expected error message to match actual implementation
        # NoOrdersLogs exception format: "No audit logs found for account {order}."
        test_account = "test_account"
        mock_repository.get_orders.side_effect = NoOrdersLogs(test_account)

        # Act & Assert
        with pytest.raises(NoOrdersLogs) as exc_info:
            orders_service.get_orders(
                account_id=None,
                customer_id=None,
                customer_account_name=None,
                order_date=None,
                customer_name=None,
                customer_email=None,
                customer_contact_no=None,
                status_type=None,
                pagination=None,
                ordering=None,
                searching=None,
            )

        assert str(exc_info.value) == f"No audit logs found for account {test_account}."
        mock_repository.get_orders.assert_called_once()
