"""create status_history table

Revision ID: f6a7b8c9d0e1
Revises: e5f6a7b8c9d0
Create Date: 2025-05-21 17:50:00.000000

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects.postgresql import UUID

# revision identifiers, used by Alembic.
revision = "f6a7b8c9d0e1"
down_revision = "e5f6a7b8c9d0"
branch_labels = None
depends_on = None


def upgrade():
    # status_history
    op.create_table(
        "status_history",
        sa.Column("id", sa.Integer, primary_key=True, autoincrement=True),
        sa.Column("uuid", UUID(as_uuid=True), nullable=False),
        sa.Column("status_name", sa.String(length=50)),
        sa.Column("status_date", sa.TIMESTAMP, server_default=sa.func.now()),
        sa.Column("comments", sa.String(length=250), nullable=True),
        sa.Column("order_id", UUID, sa.<PERSON>ey("orders.orders.uuid")),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("uuid"),
        schema="orders",
    )


def downgrade():
    op.drop_table("status_history", schema="orders")
