import uuid
from datetime import datetime
from typing import List

from pydantic import parse_obj_as

from api.orders.examples import CREATE_ORDER_REQUEST, CREATE_ORDER_RESPONSE
from api.schema_types import CamelBaseModel
from orders.domain import model


class CreateOrderRequest(CamelBaseModel):
    order_by: str
    customer_details: model.OrderCustomer
    shipping_details: model.OrderShippingDetails
    order_items: List[model.OrderItem]
    order_status_history: model.OrderStatusHistory | None = None

    class Config:
        schema_extra = {"example": CREATE_ORDER_REQUEST}

    def to_model(self) -> "model.OrderRequest":
        # Ensure order_status_history is never None, provide a default if missing
        order_status_history = self.order_status_history
        if order_status_history is None:
            order_status_history = model.OrderStatusHistory()
        return model.OrderRequest(
            order_by=self.order_by,
            customer_details=self.customer_details,
            shipping_details=self.shipping_details,
            order_items=[item for item in self.order_items],
            order_status_history=order_status_history,
        )


# Example usage
parsed_order = parse_obj_as(CreateOrderRequest, CREATE_ORDER_REQUEST)


class CreateOrderResponse(CamelBaseModel):
    uuid: uuid.UUID
    message: str

    @classmethod
    def to_model(cls, order: model.OrderResponse) -> "CreateOrderResponse":
        return cls(
            uuid=order.order_uuid,
            message=order.message,
        )

    class Config:
        schema_extra = {"example": CREATE_ORDER_RESPONSE}
        orm_mode = True


class TrackingInfo(CamelBaseModel):
    reference_id: str | None = None
    reference_url: str | None = None


class UpdateOrderStatusRequest(CamelBaseModel):
    status: str
    tracking: TrackingInfo | None = None
    comments: str | None = None


class UpdateOrderStatusResponse(CamelBaseModel):
    order_id: uuid.UUID
    status: str
    message: str = "Order status updated successfully"

    class Config:
        schema_extra = {"example": CREATE_ORDER_RESPONSE}
        orm_mode = True

    @staticmethod
    def to_model(data) -> "UpdateOrderStatusResponse":
        return UpdateOrderStatusResponse(
            order_id=data.order_id,
            status=data.status,
            message=(
                data.message
                if hasattr(data, "message")
                else "Order status updated successfully"
            ),
        )


class OrderItem(CamelBaseModel):
    type: str
    quantity: int


class OrdersDataResponse(CamelBaseModel):
    order_id: uuid.UUID
    order_by: str
    customer_account_name: str
    order_date: datetime
    customer_name: str
    person_placing_order: str
    customer_email: str
    customer_phone: str
    order_status_history: str
    order_item: List[OrderItem]
    customer_account_logo_url: str | None = None

    @classmethod
    def from_model(cls, order_data: model.OrdersData) -> "OrdersDataResponse":
        return cls(
            order_id=order_data.order_id,
            order_by=order_data.order_by,
            customer_account_name=order_data.customer_account_name,
            customer_account_logo_url=order_data.customer_account_logo_url,
            order_date=order_data.order_date,
            customer_name=order_data.customer_name,
            customer_email=order_data.customer_email,
            person_placing_order=order_data.person_placing_order,
            customer_phone=order_data.customer_phone,
            order_status_history=order_data.order_status_history,
            order_item=order_data.order_item,
        )


class OrderDetailsResponse(CamelBaseModel):
    id: int
    order_by: str
    order_id: uuid.UUID
    order_date: datetime
    customer_details: model.OrderCustomer
    shipping_details: model.OrderShippingDetails
    order_items: List[model.OrderItem]
    order_status_history: model.OrderStatusHistory
    order_tracking: model.OrderTracking | None = None

    class Config:
        schema_extra = {"example": CREATE_ORDER_REQUEST}

    @classmethod
    def from_model(
        cls, order_details: model.OrderDetailsResponse
    ) -> "OrderDetailsResponse":
        return cls(
            id=order_details.id,
            order_id=order_details.order_id,
            order_date=order_details.order_date,
            order_by=order_details.order_by,
            customer_details=order_details.customer_details,
            shipping_details=order_details.shipping_details,
            order_items=[item for item in order_details.order_items],
            order_status_history=order_details.order_status_history,
            order_tracking=order_details.order_tracking,
        )
