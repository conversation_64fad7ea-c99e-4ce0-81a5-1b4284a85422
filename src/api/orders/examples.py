CREATE_ORDER_REQUEST = {
    "customer_details": {
        "customer_name": "Kyivstar",
        "customer_account_name": "BTPushCDR",
        "customer_account_id": "077ba04b-82f6-41a6-9d54-5a793a28fee6",
        "customer_account_logo_url": "https://example.com/logo.png",
        "customer_id": "12345",
        "customer_email": "<EMAIL>",
        "person_placing_order": "Test User",
        "customer_contact_no": "**********",
        "customer_reference": "Ref123",
    },
    "shipping_details": {
        "contact_name": "<PERSON>",
        "address_line1": "Address 1",
        "address_line2": "Address 1",
        "city": "Bristol",
        "state_or_region": "South West England",
        "postal_code": "BS1 2AB",
        "country": "United Kingdom",
        "other_information": "",
    },
    "order_items": [
        {"sim_type": "2FF (Mini) SIMs", "quantity": 1500},
        {"sim_type": "3FF (Micro) SIMs", "quantity": 500},
    ],
    "order_by": "John Doe",
}

CREATE_ORDER_RESPONSE = {
    "order_id": "ba6f3a9d-9d73-4620-935b-bbf9dd9921ab",
}

UPDATE_ORDER_REQUEST = {
    "order_id": "12345",
    "status": "Shipped",
    "message": "Order status updated successfully",
}

ORDER_1 = {
    "order_id": 1,
    "customer_account_name": "Acme Corp",
    "customer_account_logo_url": "https://cdn.acme/logo.png",
    "order_date": "2024-06-01T12:34:56",
    "person_placing_order": "Test User",
    "customer_name": "John Doe",
    "customer_email": "<EMAIL>",
    "customer_phone": "**********",
    "order_status_history": "Approved",
    "sim_details": [{"type": "4G", "quantity": 10}, {"type": "5G", "quantity": 5}],
}

ORDER_2 = {
    "order_id": 2,
    "customer_account_name": "Acme Corp 2",
    "customer_account_logo_url": "https://cdn.acme/logo.png",
    "order_date": "2024-06-01T12:34:56",
    "person_placing_order": "Test User",
    "customer_name": "John Doe 2",
    "customer_email": "<EMAIL>",
    "customer_phone": "**********2",
    "order_status_history": "Approved",
    "sim_details": [{"type": "4G", "quantity": 10}, {"type": "5G", "quantity": 5}],
}
