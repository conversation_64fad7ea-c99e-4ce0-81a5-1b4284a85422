from abc import ABC, abstractmethod
from typing import List
from uuid import UUID

from api.orders.schemas import UpdateOrderStatusRequest, UpdateOrderStatusResponse
from orders.adapters.repository import AbstractOrdersRepository
from orders.domain import model


class AbstractOrdersService(ABC):
    @abstractmethod
    def create_order(self, order: model.OrderRequest) -> model.OrderResponse:
        ...

    @abstractmethod
    def update_order_status(
        self, order_id: UUID, update_data: UpdateOrderStatusRequest
    ) -> UpdateOrderStatusResponse:
        ...

    @abstractmethod
    def get_orders(
        self,
        account_id,
        customer_id,
        customer_account_name,
        order_date,
        customer_name,
        customer_email,
        customer_contact_no,
        status_type,
        pagination,
        ordering,
        searching,
    ) -> tuple[List[model.OrdersData], int]:
        ...

    @abstractmethod
    def get_order_details(self, order_id: UUID) -> model.OrderDetailsResponse:
        ...


class OrdersService(AbstractOrdersService):
    def __init__(
        self,
        orders_repository: AbstractOrdersRepository,
    ):
        self.orders_repository = orders_repository

    def create_order(self, order: model.OrderRequest) -> model.OrderResponse:
        order_response = self.orders_repository.create_order(order=order)
        return order_response

    def update_order_status(
        self, order_id: UUID, update_data: UpdateOrderStatusRequest
    ) -> UpdateOrderStatusResponse:
        order_response = self.orders_repository.update_order_status(
            order_id=order_id, update_data=update_data
        )
        return order_response

    def get_orders(
        self,
        account_id,
        customer_id,
        customer_account_name,
        order_date,
        customer_name,
        customer_email,
        customer_contact_no,
        status_type,
        pagination,
        ordering,
        searching,
    ) -> tuple[List[model.OrdersData], int]:

        response = self.orders_repository.get_orders(
            account_id=account_id,
            customer_id=customer_id,
            customer_account_name=customer_account_name,
            order_date=order_date,
            customer_name=customer_name,
            customer_email=customer_email,
            customer_contact_no=customer_contact_no,
            status_type=status_type,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )
        count = self.orders_repository.get_orders_count(
            account_id=account_id,
            customer_id=customer_id,
            customer_account_name=customer_account_name,
            order_date=order_date,
            customer_name=customer_name,
            customer_email=customer_email,
            customer_contact_no=customer_contact_no,
            status_type=status_type,
            searching=searching,
        )
        return response, count

    def get_order_details(self, order_id: UUID) -> model.OrderDetailsResponse:
        order_details = self.orders_repository.get_order_details(order_id=order_id)
        return order_details
