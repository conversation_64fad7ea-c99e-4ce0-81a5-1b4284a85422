from typing import List, <PERSON><PERSON>
from uuid import UUID

from api.orders.schemas import UpdateOrderStatusRequest, UpdateOrderStatusResponse
from auth.dto import AuthenticatedUser
from orders.domain import model
from orders.services import AbstractOrdersService


class OrdersServiceAuthProxy(AbstractOrdersService):
    def __init__(
        self,
        orders_service: AbstractOrdersService,
        user: AuthenticatedUser,
    ) -> None:
        self.orders_service = orders_service

    def create_order(self, order: model.OrderRequest) -> model.OrderResponse:
        return self.orders_service.create_order(order=order)
        # if is_distributor_staff(self.user):
        #     return self.orders_service.create_order(order=order)
        # else:
        #     raise ForbiddenError

    def update_order_status(
        self, order_id: UUID, update_data: UpdateOrderStatusRequest
    ) -> UpdateOrderStatusResponse:
        return self.orders_service.update_order_status(
            order_id=order_id, update_data=update_data
        )

    def get_orders(
        self,
        account_id,
        customer_id,
        customer_account_name,
        order_date,
        customer_name,
        customer_email,
        customer_contact_no,
        status_type,
        pagination,
        ordering,
        searching,
    ) -> Tuple[List[model.OrdersData], int]:

        return self.orders_service.get_orders(
            account_id=account_id,
            customer_id=customer_id,
            customer_account_name=customer_account_name,
            order_date=order_date,
            customer_name=customer_name,
            customer_email=customer_email,
            customer_contact_no=customer_contact_no,
            status_type=status_type,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )

    def get_order_details(self, order_id: UUID) -> model.OrderDetailsResponse:
        return self.orders_service.get_order_details(order_id=order_id)

        # if is_distributor_staff(self.user):
        #     return self.orders_service.get_order_details(order_id=order_id)
        # else:
        #     raise ForbiddenError
