import logging
from functools import cache
from typing import Callable, Generator
from uuid import UUID

from fastapi import Depends, <PERSON><PERSON>, HTTPException, status
from sqlalchemy.orm import Session

import auth.dto
from accounts.adapters.repository import DatabaseAccountRepository
from accounts.domain.ports import AbstractAccountRepository
from app.config import settings
from app.db.session import make_session
from app.security import make_auth_service_factory
from auth.dto import AuthenticatedActor, AuthenticatedUser
from auth.permissions import is_distributor_staff
from auth.services import AbstractAuthService, AccountAuthService, FakeAuthService
from common.utils import trace_id_var
from orders.adapters.repository import (
    AbstractOrdersRepository,
    DatabaseOrdersRepository,
)
from orders.proxies import OrdersServiceAuthProxy
from orders.services import AbstractOrdersService, OrdersService


def get_trace_id(x_trace_id: UUID = Header(None)):
    return trace_id_var.get()


def get_db_session() -> Generator[Session, None, None]:
    session = make_session()
    try:
        yield session
    except Exception as e:
        logging.info(f"Iteration exceeded. Error {e}")
        if hasattr(e, "detail") or hasattr(e, "status_code"):
            if hasattr(e, "detail"):
                logging.info(f"{e.detail}")  # type: ignore
            if hasattr(e, "status_code"):
                logging.info(f"{e.status_code}")  # type: ignore
        else:
            logging.info(f"Iteration exceeded. Error {e}")
    finally:
        session.close()


@cache
def _fake_auth_service() -> AbstractAuthService:
    return FakeAuthService()


_get_auth_service: Callable[[], AbstractAuthService]
if not settings.OIDC_CLIENT_ID:
    _get_auth_service = _fake_auth_service
else:
    _get_auth_service = make_auth_service_factory()


def _get_account_repository(
    session: Session = Depends(get_db_session),
) -> AbstractAccountRepository:
    return DatabaseAccountRepository(session)


def get_auth_service(
    auth_service: AbstractAuthService = Depends(_get_auth_service),
    account_repository: AbstractAccountRepository = Depends(_get_account_repository),
) -> AbstractAuthService:
    def get_auth_account(organization_id: int) -> auth.dto.Account | None:
        account = account_repository.get_by_organization_id(organization_id)
        if account is None:
            return None
        return auth.dto.Account(id=account.id)

    return AccountAuthService(auth_service, get_auth_account)


def get_authenticated_user(
    auth_service: AbstractAuthService = Depends(get_auth_service),
) -> AuthenticatedUser:
    return auth_service.authenticated_user


def get_authenticated_actor(
    auth_service: AbstractAuthService = Depends(get_auth_service),
) -> AuthenticatedActor:
    return auth_service.authenticated_actor


def distributor_access_level(exc_status_code: int = status.HTTP_403_FORBIDDEN):
    def factory(
        authenticated_user: AuthenticatedUser = Depends(get_authenticated_user),
    ):
        if not is_distributor_staff(authenticated_user):
            raise HTTPException(exc_status_code)

    return factory


def get_orders_repository(
    session: Session = Depends(get_db_session),
) -> AbstractOrdersRepository:
    return DatabaseOrdersRepository(session)


def orders_service(
    orders_repository: AbstractOrdersRepository = Depends(get_orders_repository),
    authenticated_user: AuthenticatedUser = Depends(get_authenticated_user),
) -> AbstractOrdersService:
    logging.error("Calling deps service")
    return OrdersServiceAuthProxy(
        orders_service=OrdersService(
            orders_repository,
        ),
        user=authenticated_user,
    )
