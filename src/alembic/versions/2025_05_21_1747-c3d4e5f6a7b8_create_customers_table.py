"""create customers table

Revision ID: c3d4e5f6a7b8
Revises: a1b2c3d4e5f6
Create Date: 2025-05-21 17:47:00.000000

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects.postgresql import UUID

# revision identifiers, used by Alembic.
revision = "c3d4e5f6a7b8"
down_revision = "a1b2c3d4e5f6"
branch_labels = None
depends_on = None


def upgrade():
    # customers
    op.create_table(
        "customers",
        sa.Column("id", sa.Integer, primary_key=True, autoincrement=True),
        sa.Column(
            "uuid",
            UUID(as_uuid=True),
            nullable=False,
        ),
        sa.<PERSON>umn("customer_name", sa.String(length=255)),
        sa.<PERSON>umn("customer_id", sa.Integer),
        sa.<PERSON>umn("customer_account_name", sa.String(length=255)),
        sa.Column("customer_account_id", UUID),
        sa.Column("customer_account_logo_url", sa.String(length=255)),
        sa.Column("customer_email", sa.String(length=255)),
        sa.Column("customer_contact_no", sa.String(length=50)),
        sa.Column("person_placing_order", sa.String(length=255)),
        sa.Column("customer_reference", sa.String(length=255)),
        sa.Column("order_id", UUID, sa.ForeignKey("orders.orders.uuid")),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("uuid"),
        schema="orders",
    )


def downgrade():
    op.drop_table("customers", schema="orders")
