from datetime import datetime
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Request
from starlette import status

import api.deps as trace_id_deps
from api.decorators import measure_execution_time
from api.orders import deps
from api.orders.schemas import (
    CreateOrderRequest,
    CreateOrderResponse,
    OrdersDataResponse,
    UpdateOrderStatusRequest,
    UpdateOrderStatusResponse,
)
from api.schema_types import PaginatedResponse
from app.config import logger
from auth.exceptions import AuthException, Unauthorized
from common.ordering import Ordering
from common.pagination import Pagination
from common.parser import ParsingError
from common.searching import Searching
from orders.constants import OrderStatus
from orders.exceptions import NoOrdersLogs
from orders.services import AbstractOrdersService

router = APIRouter(tags=["orders"])


@router.post("/order", status_code=status.HTTP_201_CREATED)
# @check_permissions
@measure_execution_time
def create_order(
    request: Request,
    order: CreateOrderRequest,
    orders_service: AbstractOrdersService = Depends(deps.orders_service),
    # authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> CreateOrderResponse:
    try:
        # Convert CreateOrderRequest to OrderRequest before passing to orders_service
        order_model = order.to_model()
        service_response = orders_service.create_order(order=order_model)
        response = CreateOrderResponse.to_model(service_response)
        return response
    except ParsingError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to create order - {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Couldn't process the order request",
        )
    except AuthException as e:
        logger.error(f"Error during order Create: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.patch("/orders/{order_id}/status", status_code=status.HTTP_200_OK)
# @check_permissions  # Uncomment if using permission checks
@measure_execution_time
def update_order_status(
    request: Request,
    order_id: UUID,
    update_request: UpdateOrderStatusRequest,
    orders_service: AbstractOrdersService = Depends(deps.orders_service),
    # authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> UpdateOrderStatusResponse:
    try:
        logger.info(
            f"[{trace_id}] Updating SIM order {order_id} status to "
            f"{update_request.status}"
        )
        service_response = orders_service.update_order_status(
            order_id=order_id,
            update_data=update_request,
        )
        return UpdateOrderStatusResponse.to_model(service_response)

    except ParsingError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except AuthException as e:
        logger.error(f"[{trace_id}] Auth error during order status update: {str(e)}")
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail=str(e))
    except Exception as e:
        logger.error(f"[{trace_id}] Failed to update order status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Couldn't update the order status",
        )


@router.get(
    "/order",
    status_code=status.HTTP_200_OK,
    response_model=PaginatedResponse[OrdersDataResponse],
)
def get_orders(
    request: Request,
    account_id: int | None = None,
    customer_id: int | None = None,
    customer_account_name: str | None = None,
    order_date: datetime | None = None,
    customer_name: str | None = None,
    customer_email: datetime | None = None,
    customer_contact_no: str | None = None,
    status_type: OrderStatus | None = None,
    orders_service: AbstractOrdersService = Depends(deps.orders_service),
    # authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    pagination: Pagination = Depends(Pagination.query()),
    ordering: Ordering = Depends(
        Ordering.query(
            OrdersDataResponse,
            default_ordering="order_date",
            ordering_fields=(
                "order_id",
                "order_date",
                "customer_account_name",
                "person_placing_order",
                "customer_contact_no",
                "account_id",
                "customer_id",
                "customer_name",
                "customer_email",
            ),
        )
    ),
    searching: Searching
    | None = Depends(
        Searching.build(
            fields={
                "order_id",
                "order_date",
                "customer_account_name",
                "person_placing_order",
                "customer_contact_no",
                "account_id",
                "customer_id",
                "customer_name",
                "customer_email",
            },
        )
    ),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> PaginatedResponse[OrdersDataResponse]:
    try:
        records, total_count = orders_service.get_orders(
            account_id=account_id,
            customer_id=customer_id,
            customer_account_name=customer_account_name,
            order_date=order_date,
            customer_name=customer_name,
            customer_email=customer_email,
            customer_contact_no=customer_contact_no,
            status_type=status_type,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )
        return PaginatedResponse.from_iterable(
            pagination=pagination,
            results=list(map(OrdersDataResponse.from_model, records)),  # type: ignore
            total_count=total_count,
        )
    except Unauthorized as e:
        logger.error(f"Unauthorized.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access."
        )
    except NoOrdersLogs as e:
        logger.error(f"No order logs error occoured.:{str(e)} ")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="No order logs found."
        )
    except ValueError as e:
        logger.error(f"value error occoured.:{str(e)} ")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Couldn't process your request",
        )
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )
